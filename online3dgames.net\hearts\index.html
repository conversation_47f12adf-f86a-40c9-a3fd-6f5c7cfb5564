<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-49EMLQ4Q49"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-49EMLQ4Q49');
</script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hearts Card Game - Classic 4-Player Online Hearts | Online3DGames</title>
    <meta name="description" content="Play the classic Hearts card game online for free. Avoid penalty cards, master the passing strategy, and try to shoot the moon in this challenging 4-player card game with smart AI opponents.">
    <meta name="keywords" content="hearts card game, hearts online, card games, classic hearts, shooting the moon, penalty cards, queen of spades, hearts strategy, online card games, free hearts game">
    <meta name="author" content="Online3DGames">
    
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <meta name="theme-color" content="#1a4d2e">
    
    <link rel="preload" href="/hearts/css/styles.css" as="style">
    <link rel="stylesheet" href="/hearts/css/styles.css">
    <link rel="stylesheet" href="/assets/css/game-recommendations.css">
</head>
<body>
    <div id="landscape-prompt" class="landscape-prompt">
        <div class="landscape-content">
            <div class="rotate-icon">📱</div>
            <h2>Better Experience in Landscape</h2>
            <p>Please rotate your device to landscape mode for the best Hearts experience</p>
        </div>
    </div>

    <div class="game-container">
        <header class="game-header">
            <div class="header-left">
                <h1>Hearts</h1>
                <div class="game-info">
                    <div class="round-info">
                        <span class="info-label">Round:</span>
                        <span id="currentRound">1</span>
                    </div>
                    <div class="trick-info">
                        <span class="info-label">Trick:</span>
                        <span id="currentTrick">1</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <a href="/" class="btn btn-secondary">Home</a>
                <button id="viewScoresBtn" class="btn btn-secondary">Scores</button>
                <button id="newGameBtn" class="btn btn-primary">New Game</button>
                <button id="helpBtn" class="btn btn-secondary">Help</button>
                <button id="fullscreenBtn" class="btn btn-secondary">⛶</button>
            </div>
        </header>

        <main class="game-board">
            <div class="play-area">
                <div class="player-area north" id="player-north">
                    <div class="player-hand"></div>
                    <div class="player-info">
                        <span class="player-name">Bob</span>
                        <span class="player-score" id="score-north">0</span>
                        <span class="cards-count">13</span>
                    </div>
                </div>

                <div class="center-area">
                    <div class="player-area west" id="player-west">
                        <div class="player-hand"></div>
                        <div class="player-info">
                            <span class="player-name">Alice</span>
                            <span class="player-score" id="score-west">0</span>
                            <span class="cards-count">13</span>
                        </div>
                    </div>

                    <div class="trick-area">
                        <div class="played-card north-card" id="played-north"></div>
                        <div class="played-card west-card" id="played-west"></div>
                        <div class="played-card east-card" id="played-east"></div>
                        <div class="played-card south-card" id="played-south"></div>
                        <div class="trick-winner-indicator" id="trickWinner"></div>

                        <div class="passing-slots" id="passingSlots" style="display: none;">
                            <div class="passing-slot" data-slot="1">
                                <div class="slot-number">1</div>
                            </div>
                            <div class="passing-slot" data-slot="2">
                                <div class="slot-number">2</div>
                            </div>
                            <div class="passing-slot" data-slot="3">
                                <div class="slot-number">3</div>
                            </div>
                            <div class="passing-button-container">
                                <button id="passCardsBtn" class="btn btn-primary" disabled>Pass Cards</button>
                            </div>
                        </div>
                    </div>

                    <div class="player-area east" id="player-east">
                        <div class="player-hand"></div>
                        <div class="player-info">
                            <span class="player-name">Charlie</span>
                            <span class="player-score" id="score-east">0</span>
                            <span class="cards-count">13</span>
                        </div>
                    </div>
                </div>

                <div class="player-area south" id="player-south">
                    <div class="player-info">
                        <span class="player-name">You</span>
                        <span class="player-score" id="score-south">0</span>
                        <span class="cards-count">13</span>
                    </div>
                    <div class="player-hand"></div>
                </div>
            </div>

            <div class="game-status">
                <div class="status-message" id="statusMessage">Welcome to Hearts! Click New Game to start.</div>
                <div class="passing-info" id="passingInfo"></div>
            </div>
        </main>





        <div id="scoreboardModal" class="modal hidden">
            <div class="modal-content scoreboard-modal">
                <h2 id="scoreboardTitle">Detailed Game Results</h2>
                <div class="scoreboard-container">
                    <div class="game-summary" id="gameSummary"></div>
                    <div class="scoreboard-body" id="scoreboardBody"></div>
                </div>
                <div class="modal-buttons">
                    <button id="continueBtn" class="btn btn-primary">Continue Game</button>
                    <button id="scoreboardNewGameBtn" class="btn btn-secondary" style="display: none;">New Game</button>
                </div>
            </div>
        </div>

        <div id="gameResumeModal" class="modal hidden">
            <div class="modal-content game-resume-modal">
                <h2 id="gameResumeTitle">Game in Progress</h2>
                <div class="game-resume-container">
                    <div class="resume-summary" id="resumeSummary"></div>
                    <div class="resume-scoreboard" id="resumeScoreboard"></div>
                </div>
                <div class="modal-buttons">
                    <button id="resumeGameBtn" class="btn btn-primary">Resume Game</button>
                    <button id="startNewGameBtn" class="btn btn-secondary">Start New Game</button>
                </div>
            </div>
        </div>

        <div id="gameOverModal" class="modal hidden">
            <div class="modal-content">
                <h2 id="gameOverTitle">Game Over!</h2>
                <div class="final-scores" id="finalScores"></div>
                <div class="modal-buttons">
                    <button id="newGameFromModal" class="btn btn-primary">New Game</button>
                    <button id="closeGameOver" class="btn btn-secondary">Close</button>
                </div>
            </div>
        </div>

        <div id="helpPanel" class="help-panel hidden">
            <div class="help-content">
                <div class="help-header">
                    <h3>♥ Hearts Card Game</h3>
                    <button id="closeHelpBtn" class="close-btn">×</button>
                </div>
                <div class="help-body">
                    <div class="help-section">
                        <h4>🎯 Game Objective</h4>
                        <p>Avoid penalty points! Each heart is worth 1 point and the Queen of Spades is worth 13 points. The player with the lowest score when someone reaches 100 points wins.</p>
                    </div>
                    <div class="help-section">
                        <h4>📋 Game Rules</h4>
                        <ul>
                            <li><strong>Dealing:</strong> Each player gets 13 cards from a standard 52-card deck</li>
                            <li><strong>Passing:</strong> Before each hand, pass 3 cards to another player (direction rotates)</li>
                            <li><strong>Leading:</strong> The player with the 2 of Clubs leads the first trick</li>
                            <li><strong>Following:</strong> Players must follow suit if possible, otherwise play any card</li>
                            <li><strong>Hearts Breaking:</strong> Hearts cannot be led until hearts have been "broken" (played on a trick)</li>
                            <li><strong>Queen of Spades:</strong> Cannot be played on the first trick unless it's the only spade</li>
                        </ul>
                    </div>
                    <div class="help-section">
                        <h4>🌙 Shooting the Moon</h4>
                        <p>If a player takes all hearts and the Queen of Spades (26 points total), they score 0 points and all other players get 26 points added to their score.</p>
                    </div>
                    <div class="help-section">
                        <h4>🏆 Winning</h4>
                        <p>The game ends when any player reaches 100 points. The player with the lowest total score wins!</p>
                    </div>
                </div>
                <div class="help-footer">
                    <button id="closeHelpBtnBottom" class="btn btn-primary">Start Playing!</button>
                </div>
            </div>
        </div>
    </div>

    <!-- SEO Content Section -->
    <div class="seo-content-section" style="max-width: 1200px; margin: 40px auto; padding: 20px; font-family: Arial, sans-serif; line-height: 1.6; position: relative; z-index: 1; border-radius: 10px;">
        <h2 style="color: #ffffff; margin-bottom: 20px; font-size: 28px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">Hearts Card Game - Master the Classic 4-Player Strategy</h2>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">♥️ Classic Hearts Gameplay</h3>
                <p style="margin-bottom: 15px; color: #f0f0f0;">Experience the timeless strategy of Hearts, the classic trick-taking card game that challenges players to avoid penalty points while mastering the art of card passing and strategic play.</p>
                <p style="margin-bottom: 15px; color: #f0f0f0;">Our Hearts game features intelligent AI opponents with varying difficulty levels, realistic card animations, and authentic gameplay that follows traditional Hearts rules.</p>
            </div>

            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🎯 Strategic Features</h3>
                <ul style="margin-bottom: 15px; padding-left: 20px; color: #f0f0f0;">
                    <li><strong style="color: #ffffff;">Smart AI Players:</strong> Advanced computer opponents</li>
                    <li><strong style="color: #ffffff;">Card Passing:</strong> Strategic 3-card passing rounds</li>
                    <li><strong style="color: #ffffff;">Shooting the Moon:</strong> High-risk, high-reward strategy</li>
                    <li><strong style="color: #ffffff;">Score Tracking:</strong> Comprehensive game statistics</li>
                    <li><strong style="color: #ffffff;">Mobile Optimized:</strong> Perfect for tablet and desktop play</li>
                </ul>
            </div>
        </div>

        <div style="margin-bottom: 30px;">
            <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🌙 Advanced Hearts Strategy</h3>
            <p style="margin-bottom: 15px; color: #f0f0f0;">Master the art of Hearts with our comprehensive strategy guide. Learn when to pass dangerous cards, how to track played cards, and when to attempt shooting the moon for maximum points.</p>
            <p style="margin-bottom: 15px; color: #f0f0f0;">The game features rotating passing directions (left, right, across, no pass) and intelligent AI that adapts to your playing style, providing endless replayability and strategic depth.</p>
        </div>

        <div style="padding: 20px; margin-bottom: 20px;">
            <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 20px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🏆 The Ultimate Hearts Experience</h3>
            <p style="margin-bottom: 10px; color: #f0f0f0;">Our Hearts game combines traditional gameplay with modern features, offering smooth animations, intuitive controls, and challenging AI opponents that make every game exciting.</p>
            <p style="color: #f0f0f0;">Whether you're a Hearts veteran or learning the game for the first time, our comprehensive help system and strategic gameplay will keep you engaged for hours!</p>
        </div>
    </div>

                                    <!-- Game Recommendations -->
    <div class="game-recommendations similar-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title">More Card Games</h3>
            <p class="recommendations-subtitle">Continue exploring card games</p>
        </div>
        <div class="recommendations-grid">
            <div class="recommendations-row">
                <a href="/solitaire" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🃏</div>
                        <h4 class="game-name">Solitaire</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/spider-solitaire" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🕷️</div>
                        <h4 class="game-name">Spider Solitaire</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/texas-holdem-game" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🎲</div>
                        <h4 class="game-name">Texas Hold'em</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/spades" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">♠️</div>
                        <h4 class="game-name">Spades</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
    <div class="game-recommendations other-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title">Other Games</h3>
            <p class="recommendations-subtitle">Discover more amazing games</p>
        </div>
        <div class="recommendations-grid">
            <div class="recommendations-row">
                <a href="/2048" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🔢</div>
                        <h4 class="game-name">2048</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/blackjack" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🎰</div>
                        <h4 class="game-name">Blackjack</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/blackjack-practice" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🎯</div>
                        <h4 class="game-name">Blackjack Practice</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/freeBetBlackjack" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🆓</div>
                        <h4 class="game-name">Free Bet Blackjack</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/pontoon-game" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🚤</div>
                        <h4 class="game-name">Pontoon</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row offset-row">
                <a href="/chess" class="recommendation-card" data-category="strategy">
                    <div class="card-image">
                        <div class="game-icon">♔</div>
                        <h4 class="game-name">Chess</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/sudoku-game" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🧩</div>
                        <h4 class="game-name">Sudoku</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/tetris-game" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🟦</div>
                        <h4 class="game-name">Tetris</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/memoryGame" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🧠</div>
                        <h4 class="game-name">Memory Game</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row">
                <a href="/snake-game" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">🐍</div>
                        <h4 class="game-name">Snake</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/breakout-game" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">🧱</div>
                        <h4 class="game-name">Breakout</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/reaction-test" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">⚡</div>
                        <h4 class="game-name">Reaction Test</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/pop-it-game" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🫧</div>
                        <h4 class="game-name">Pop-it</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
                <a href="/fidget-spinner" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🌀</div>
                        <h4 class="game-name">Fidget Spinner</h4>
                        <div class="game-rating">⭐ 4.3</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row offset-row">
                <a href="/breathing-ball" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🌬️</div>
                        <h4 class="game-name">Breathing Ball</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/drawing-wall" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🎨</div>
                        <h4 class="game-name">Drawing Wall</h4>
                        <div class="game-rating">⭐ 4.2</div>
                    </div>
                </a>
                <a href="/bubble-float" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">💭</div>
                        <h4 class="game-name">Bubble Float</h4>
                        <div class="game-rating">⭐ 4.1</div>
                    </div>
                </a>
                <a href="/particle-trail" class="recommendation-card" data-category="other">
                    <div class="card-image">
                        <div class="game-icon">✨</div>
                        <h4 class="game-name">Particle Trail</h4>
                        <div class="game-rating">⭐ 4.3</div>
                    </div>
                </a>
                <a href="/typing-machine" class="recommendation-card" data-category="other">
                    <div class="card-image">
                        <div class="game-icon">⌨️</div>
                        <h4 class="game-name">Typing Machine</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
                <a href="/virtual-pet" class="recommendation-card" data-category="other">
                    <div class="card-image">
                        <div class="game-icon">🐱</div>
                        <h4 class="game-name">Virtual Pet</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
    <script src="/assets/js/jquery-3.7.1.min.js"></script>
    <script src="/hearts/js/game.js"></script>
    <script src="/assets/js/game-recommendations.js"></script>
</body>
</html>